import 'dart:io';

/// Final comprehensive architecture analysis to verify four-layer dependency rule compliance
/// after all modifications and improvements
void main() async {
  print('🏗️  FINAL ARCHITECTURE COMPLIANCE ANALYSIS');
  print('=' * 60);
  print('Verifying four-layer dependency rule after all modifications\n');

  final analysisResults = <String, bool>{};

  // Analysis 1: Layer Structure Verification
  analysisResults['Layer Structure'] = await verifyLayerStructure();

  // Analysis 2: Dependency Direction Analysis
  analysisResults['Dependency Direction'] = await verifyDependencyDirection();

  // Analysis 3: Interface Abstraction Compliance
  analysisResults['Interface Abstractions'] =
      await verifyInterfaceAbstractions();

  // Analysis 4: Code Duplication Analysis
  analysisResults['Code Duplication'] = await verifyCodeDuplication();

  // Analysis 5: Clean Architecture Principles
  analysisResults['Clean Architecture'] = await verifyCleanArchitecture();

  // Analysis 6: Performance Optimization Compliance
  analysisResults['Performance Optimization'] =
      await verifyPerformanceOptimization();

  // Print Final Results
  print('\n🎯 FINAL ARCHITECTURE ANALYSIS RESULTS');
  print('=' * 60);

  int passed = 0;
  int total = analysisResults.length;

  analysisResults.forEach((category, result) {
    final status = result ? '✅ COMPLIANT' : '❌ NON-COMPLIANT';
    print('$status $category');
    if (result) passed++;
  });

  final complianceRate = (passed / total * 100);
  print('\n📊 OVERALL COMPLIANCE SCORE: $passed/$total');
  print('📊 COMPLIANCE RATE: ${complianceRate.toStringAsFixed(1)}%');

  if (passed == total) {
    print(
      '\n🎉 PERFECT COMPLIANCE! The application fully adheres to the four-layer dependency rule.',
    );
    print('🏆 All Clean Architecture principles are properly implemented.');
  } else {
    print(
      '\n⚠️  COMPLIANCE ISSUES DETECTED. Review the failed categories above.',
    );
    exit(1);
  }
}

/// Verify the four-layer structure is properly maintained
Future<bool> verifyLayerStructure() async {
  print('🔍 Verifying Layer Structure...');

  try {
    final requiredLayers = {
      'lib/presentation/': 'Presentation Layer (UI)',
      'lib/statefulbusinesslogic/': 'Business Logic Layer',
      'lib/repositories/': 'Repository Layer (Interfaces)',
      'lib/provider/': 'Provider Layer (Implementations)',
    };

    for (final entry in requiredLayers.entries) {
      final layerPath = entry.key;
      final layerName = entry.value;

      final dir = Directory(layerPath);
      if (!dir.existsSync()) {
        print('  ❌ Missing layer: $layerName at $layerPath');
        return false;
      }

      // Check if layer has content
      final files =
          dir
              .listSync(recursive: true)
              .where(
                (entity) => entity is File && entity.path.endsWith('.dart'),
              )
              .length;

      if (files == 0) {
        print('  ❌ Empty layer: $layerName');
        return false;
      }

      print('  ✅ $layerName: $files files');
    }

    print('  ✅ All four layers properly structured');
    return true;
  } catch (e) {
    print('  ❌ Layer structure verification failed: $e');
    return false;
  }
}

/// Verify dependency direction follows the four-layer rule
Future<bool> verifyDependencyDirection() async {
  print('🔍 Verifying Dependency Direction...');

  try {
    final violations = <String>[];

    // Check Presentation Layer (should only depend on Business Logic)
    final presentationViolations = await _checkLayerDependencies(
      'lib/presentation/',
      allowedDependencies: ['lib/statefulbusinesslogic/'],
      layerName: 'Presentation',
    );
    violations.addAll(presentationViolations);

    // Check Business Logic Layer (should only depend on Repository)
    final businessLogicViolations = await _checkLayerDependencies(
      'lib/statefulbusinesslogic/',
      allowedDependencies: ['lib/repositories/', 'lib/statefulbusinesslogic/'],
      layerName: 'Business Logic',
    );
    violations.addAll(businessLogicViolations);

    // Check Repository Layer (should not depend on Provider)
    final repositoryViolations = await _checkLayerDependencies(
      'lib/repositories/',
      allowedDependencies: ['lib/repositories/', 'lib/statefulbusinesslogic/'],
      layerName: 'Repository',
    );
    violations.addAll(repositoryViolations);

    if (violations.isNotEmpty) {
      print('  ❌ Dependency violations found:');
      for (final violation in violations) {
        print('    • $violation');
      }
      return false;
    }

    print('  ✅ All dependency directions comply with four-layer rule');
    return true;
  } catch (e) {
    print('  ❌ Dependency direction verification failed: $e');
    return false;
  }
}

/// Check dependencies for a specific layer
Future<List<String>> _checkLayerDependencies(
  String layerPath, {
  required List<String> allowedDependencies,
  required String layerName,
}) async {
  final violations = <String>[];
  final layerDir = Directory(layerPath);

  if (!layerDir.existsSync()) return violations;

  final dartFiles =
      layerDir
          .listSync(recursive: true)
          .where((entity) => entity is File && entity.path.endsWith('.dart'))
          .cast<File>();

  for (final file in dartFiles) {
    final content = await file.readAsString();
    final lines = content.split('\n');

    for (final line in lines) {
      if (line.trim().startsWith('import ') && line.contains('lib/')) {
        // Extract import path using simple string manipulation
        final startIndex = line.indexOf("'");
        final endIndex = line.lastIndexOf("'");

        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
          final importPath = line.substring(startIndex + 1, endIndex);

          // Check if this import violates the dependency rule
          if (importPath.startsWith('lib/')) {
            final isAllowed = allowedDependencies.any(
              (allowed) => importPath.startsWith(allowed),
            );

            if (!isAllowed) {
              violations.add(
                '$layerName layer file ${file.path} imports $importPath',
              );
            }
          }
        }
      }
    }
  }

  return violations;
}

/// Verify interface abstractions are properly implemented
Future<bool> verifyInterfaceAbstractions() async {
  print('🔍 Verifying Interface Abstractions...');

  try {
    // Check that new interfaces were created
    final newInterfaces = [
      'lib/repositories/image_processing/image_processing_repository.dart',
      'lib/repositories/cache/cache_invalidation_repository.dart',
      'lib/repositories/cache/cache_monitoring_repository.dart',
      'lib/repositories/cache/ui_cache_repository.dart',
      'lib/repositories/cache/network_cache_repository.dart',
    ];

    for (final interfacePath in newInterfaces) {
      final file = File(interfacePath);
      if (!file.existsSync()) {
        print('  ❌ Missing interface: $interfacePath');
        return false;
      }

      final content = await file.readAsString();
      if (!content.contains('abstract class')) {
        print('  ❌ Interface not abstract: $interfacePath');
        return false;
      }
    }

    // Check that implementations exist
    final implementations = [
      'lib/provider/repositories/image_processing/image_processing_repository_impl.dart',
      'lib/provider/repositories/cache/cache_invalidation_repository_impl.dart',
      'lib/provider/repositories/cache/cache_monitoring_repository_impl.dart',
      'lib/provider/repositories/cache/ui_cache_repository_impl.dart',
      'lib/provider/repositories/cache/network_cache_repository_impl.dart',
    ];

    for (final implPath in implementations) {
      final file = File(implPath);
      if (!file.existsSync()) {
        print('  ❌ Missing implementation: $implPath');
        return false;
      }

      final content = await file.readAsString();
      if (!content.contains('implements')) {
        print('  ❌ Implementation does not implement interface: $implPath');
        return false;
      }
    }

    print('  ✅ All interface abstractions properly implemented');
    return true;
  } catch (e) {
    print('  ❌ Interface abstraction verification failed: $e');
    return false;
  }
}

/// Verify code duplication has been eliminated
Future<bool> verifyCodeDuplication() async {
  print('🔍 Verifying Code Duplication Elimination...');

  try {
    // Check that unified utilities exist
    final utilities = [
      'lib/statefulbusinesslogic/core/utils/cache_key_generator.dart',
      'lib/statefulbusinesslogic/core/error/error_handler.dart',
    ];

    for (final utilityPath in utilities) {
      final file = File(utilityPath);
      if (!file.existsSync()) {
        print('  ❌ Missing utility: $utilityPath');
        return false;
      }
    }

    // Check that image processing is consolidated
    final imageProcessingService = File(
      'lib/provider/services/image_processing_service.dart',
    );
    if (!imageProcessingService.existsSync()) {
      print('  ❌ Missing consolidated image processing service');
      return false;
    }

    final content = await imageProcessingService.readAsString();
    if (!content.contains('_repository')) {
      print('  ❌ Image processing service not using repository pattern');
      return false;
    }

    print('  ✅ Code duplication successfully eliminated');
    return true;
  } catch (e) {
    print('  ❌ Code duplication verification failed: $e');
    return false;
  }
}

/// Verify Clean Architecture principles
Future<bool> verifyCleanArchitecture() async {
  print('🔍 Verifying Clean Architecture Principles...');

  try {
    // Check dependency inversion
    final repositoryDir = Directory('lib/repositories/');
    final repositoryFiles =
        repositoryDir
            .listSync(recursive: true)
            .where((entity) => entity is File && entity.path.endsWith('.dart'))
            .cast<File>();

    int abstractRepositories = 0;
    for (final file in repositoryFiles) {
      final content = await file.readAsString();
      if (content.contains('abstract class') &&
          file.path.contains('repository')) {
        abstractRepositories++;
      }
    }

    if (abstractRepositories < 5) {
      print(
        '  ❌ Insufficient abstract repositories: $abstractRepositories < 5',
      );
      return false;
    }

    // Check that business logic doesn't depend on concrete implementations
    final businessLogicDir = Directory('lib/statefulbusinesslogic/');
    final businessLogicFiles =
        businessLogicDir
            .listSync(recursive: true)
            .where((entity) => entity is File && entity.path.endsWith('.dart'))
            .cast<File>();

    for (final file in businessLogicFiles) {
      final content = await file.readAsString();
      if (content.contains('import') && content.contains('lib/provider/')) {
        // Allow specific exceptions for models and utilities
        if (!file.path.contains('model') && !file.path.contains('exception')) {
          print('  ❌ Business logic depends on provider layer: ${file.path}');
          return false;
        }
      }
    }

    print('  ✅ Clean Architecture principles properly followed');
    return true;
  } catch (e) {
    print('  ❌ Clean Architecture verification failed: $e');
    return false;
  }
}

/// Verify performance optimization compliance
Future<bool> verifyPerformanceOptimization() async {
  print('🔍 Verifying Performance Optimization Compliance...');

  try {
    // Check that performance services exist
    final performanceServices = [
      'lib/provider/services/ui_optimization_service.dart',
      'lib/provider/services/cache_monitoring_service.dart',
      'lib/provider/services/network_optimization_service.dart',
      'lib/statefulbusinesslogic/core/services/hopen_cache_manager.dart',
    ];

    for (final servicePath in performanceServices) {
      final file = File(servicePath);
      if (!file.existsSync()) {
        print('  ❌ Missing performance service: $servicePath');
        return false;
      }
    }

    // Check that cache key generation is optimized
    final cacheKeyGen = File(
      'lib/statefulbusinesslogic/core/utils/cache_key_generator.dart',
    );
    final content = await cacheKeyGen.readAsString();

    if (!content.contains('sha256') || !content.contains('_generateHash')) {
      print('  ❌ Cache key generation not optimized');
      return false;
    }

    print('  ✅ Performance optimization compliance verified');
    return true;
  } catch (e) {
    print('  ❌ Performance optimization verification failed: $e');
    return false;
  }
}
