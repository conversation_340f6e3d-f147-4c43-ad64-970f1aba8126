import 'package:flutter/material.dart';

/// A custom text field widget with vertically centered text and consistent styling
/// across the application.
///
/// Features:
/// * Fixed height of 1/16 of screen height
/// * Vertically centered input text
/// * Floating label behavior (like standard TextFormField)
/// * Error handling with visual feedback (label color and red border)
/// * Focus indication with a cyan border
/// * Consistent styling with the app design language
/// * Rounded superellipse borders for modern appearance
class CustomTextField extends StatefulWidget {

  const CustomTextField({
    required this.controller, required this.hintText, super.key,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.readOnly = false,
    this.onTap,
    this.suffixIcon,
    this.prefixIcon,
    this.fontSize = 16,
  });
  final TextEditingController controller;
  final String hintText; // Used as the base for labelText
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final bool readOnly;
  final VoidCallback? onTap;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final double? fontSize;

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _isFocused = false; // Track focus state
  bool _hasError = false; // Track error state (only for visual display)
  bool _hasText = false; // Track if field has text content
  bool _userHasInteracted = false; // Track if user has actually interacted with this field

  @override
  void initState() {
    super.initState();
    // Initialize text state
    _hasText = widget.controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _checkAndUpdateErrorState(String value) {
    // Only show visual errors if the user has actually interacted with this field
    if (widget.validator != null && _userHasInteracted) {
      final error = widget.validator!(value);
      final hasError = error != null;
      
      // Only update if error state actually changed
      if (_hasError != hasError) {
        setState(() {
          _hasError = hasError;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16; // Exactly 1/16 of screen height

    // CRITICAL VERTICAL FIX: Always render border with consistent thickness
    // Changing from null border to Border.all() shifts vertical position by 1px!
    Color borderColor;
    if (_hasError && _userHasInteracted) {
      borderColor = Colors.red;
    } else if (_isFocused) {
      borderColor = const Color(0xFF00FFFF);
    } else {
      // Transparent border maintains same thickness, preventing vertical shifts
      borderColor = Colors.transparent;
    }

    return Container(
      width: double.infinity,
      height: fieldHeight,
      decoration: ShapeDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(
            color: borderColor,
            width: 1.0,
          ),
        ),
      ),
      child: Material(
        // Material needed for InkWell effects if icons are buttons
        color: Colors.transparent,
        child: Row(
          children: [
            // Prefix icon if provided
            if (widget.prefixIcon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 12),
                child: widget.prefixIcon,
              ),
            ],

            // Text field takes remaining space
            Expanded(
              child: Focus(
                onFocusChange: (hasFocus) {
                  setState(() => _isFocused = hasFocus);
                  
                  // Mark as interacted when user focuses on the field
                  if (hasFocus && !_userHasInteracted) {
                    _userHasInteracted = true;
                  }
                  
                  // When losing focus, validate the field if user has interacted
                  if (!hasFocus && _userHasInteracted) {
                    _checkAndUpdateErrorState(widget.controller.text);
                  }
                },
                child: TextFormField(
                  controller: widget.controller,
                  obscureText: widget.obscureText,
                  keyboardType: widget.keyboardType,
                  readOnly: widget.readOnly,
                  onTap: widget.onTap,
                  validator: (value) {
                    // Always return the validation result for form validation
                    // but don't update visual state unless user has interacted
                    return widget.validator?.call(value);
                  },
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: widget.fontSize,
                    height: 1, // Important for vertical centering
                    fontFamily: 'Omnes',
                  ),
                  cursorColor: Colors.white,
                  cursorHeight: widget.fontSize,
                  decoration: InputDecoration(
                    // Core label behavior setup
                    labelText: widget.hintText,
                    labelStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontFamily: 'Omnes',
                    ),
                    floatingLabelStyle: const TextStyle(
                      // Label color changes based on focus state
                      color: Color(0xFF00FFFF),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Omnes',
                    ),
                    // CRITICAL VERTICAL FIX: Lock label position to prevent vertical text shifts
                    // Label moving from inline to floating causes text to shift vertically!
                    floatingLabelBehavior: (_userHasInteracted || _hasText)
                        ? FloatingLabelBehavior.auto // Auto behavior: goes inline when unfocused and no text
                        : FloatingLabelBehavior.never, // Stay inline until interaction

                    // Remove internal borders and default padding
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,

                    // CRITICAL VERTICAL FIX: Responsive content padding to prevent vertical text shifts
                    contentPadding: EdgeInsets.only(
                      left: widget.prefixIcon == null ? 16.0 : 8.0,
                      right: 16,
                      bottom: fieldHeight * 0.38, // RESPONSIVE: 38% of field height for perfect cursor positioning on different devices
                      top: fieldHeight * 0.1,     // RESPONSIVE: 10% of field height for consistent top spacing
                    ),

                    // CRITICAL VERTICAL FIX: Completely disable error text to prevent layout shifts
                    errorStyle: const TextStyle(height: 0.01, color: Colors.transparent),
                    errorMaxLines: 1,

                    // No helperText reserved; padding already ensures stable height.
                  ),
                  onChanged: (value) {
                    final hasText = value.isNotEmpty;
                    var needsUpdate = false;

                    // Check if text state changed
                    if (_hasText != hasText) {
                      _hasText = hasText;
                      needsUpdate = true;
                    }

                    // Mark as interacted when user types
                    if (!_userHasInteracted) {
                      _userHasInteracted = true;
                    }

                    // Only validate in real-time if the field is focused AND user is actively typing
                    if (_isFocused && _userHasInteracted) {
                      _checkAndUpdateErrorState(value);
                    }

                    // Only call setState if text state changed (not for error state)
                    if (needsUpdate) {
                      setState(() {});
                    }
                  },
                ),
              ),
            ),

            // Suffix icon if provided
            if (widget.suffixIcon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 12),
                child: widget.suffixIcon,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// A password field variant of CustomTextField with a visibility toggle
class CustomPasswordField extends StatefulWidget {

  const CustomPasswordField({
    required this.controller, required this.hintText, super.key,
    this.validator,
    this.fontSize = 16,
  });
  final TextEditingController controller;
  final String hintText;
  final String? Function(String?)? validator;
  final double? fontSize;

  @override
  State<CustomPasswordField> createState() => _CustomPasswordFieldState();
}

class _CustomPasswordFieldState extends State<CustomPasswordField> {
  bool obscureText = true;

  @override
  Widget build(BuildContext context) => CustomTextField(
      controller: widget.controller,
      hintText: widget.hintText,
      obscureText: obscureText,
      validator: widget.validator,
      fontSize: widget.fontSize,
      suffixIcon: IconButton(
        icon: Icon(
          obscureText ? Icons.visibility_off : Icons.visibility,
          color: Colors.white.withValues(alpha: 0.7),
        ),
        onPressed: () {
          setState(() {
            obscureText = !obscureText;
          });
        },
        padding: EdgeInsets.zero, // Ensure the icon doesn't affect centering
      ),
    );
}
